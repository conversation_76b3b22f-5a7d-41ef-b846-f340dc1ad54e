#!/usr/bin/env python3
"""
测试3D ResNet模型的脚本
验证模型是否能正常创建、前向传播和训练
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from resnet3D_model import resnet3d

def test_model_creation():
    """测试模型创建"""
    print("=== Testing Model Creation ===")
    
    models_to_test = ['resnet18', 'resnet34', 'resnet50']
    
    for model_name in models_to_test:
        try:
            model = resnet3d(model_name=model_name, in_channels=1, num_classes=2, init_weights=True)
            total_params = sum(p.numel() for p in model.parameters())
            trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
            
            print(f"✓ {model_name.upper()}: {total_params:,} parameters ({trainable_params:,} trainable)")
            
        except Exception as e:
            print(f"✗ {model_name.upper()}: Failed - {e}")
    
    print()

def test_forward_pass():
    """测试前向传播"""
    print("=== Testing Forward Pass ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    # 创建测试输入 (batch_size=2, channels=1, depth=64, height=128, width=128)
    test_input = torch.randn(2, 1, 64, 128, 128).to(device)
    print(f"Test input shape: {test_input.shape}")
    
    models_to_test = ['resnet18', 'resnet34']  # 测试较小的模型以节省内存
    
    for model_name in models_to_test:
        try:
            model = resnet3d(model_name=model_name, in_channels=1, num_classes=2).to(device)
            model.eval()
            
            with torch.no_grad():
                output = model(test_input)
                
            print(f"✓ {model_name.upper()}: Output shape {output.shape}, "
                  f"Range [{output.min():.3f}, {output.max():.3f}]")
            
            # 检查输出是否合理
            assert output.shape == (2, 2), f"Expected output shape (2, 2), got {output.shape}"
            assert not torch.isnan(output).any(), "Output contains NaN values"
            assert not torch.isinf(output).any(), "Output contains Inf values"
            
            del model  # 释放内存
            
        except Exception as e:
            print(f"✗ {model_name.upper()}: Failed - {e}")
    
    print()

def test_training_step():
    """测试训练步骤"""
    print("=== Testing Training Step ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 创建小的测试数据
    batch_size = 1
    test_input = torch.randn(batch_size, 1, 32, 64, 64).to(device)  # 较小的输入以节省内存
    test_labels = torch.randint(0, 2, (batch_size,)).to(device)
    
    print(f"Test input shape: {test_input.shape}")
    print(f"Test labels: {test_labels}")
    
    try:
        # 创建模型
        model = resnet3d(model_name='resnet18', in_channels=1, num_classes=2).to(device)
        
        # 定义损失函数和优化器
        criterion = nn.CrossEntropyLoss()
        optimizer = optim.Adam(model.parameters(), lr=0.001)
        
        # 训练模式
        model.train()
        
        # 前向传播
        outputs = model(test_input)
        loss = criterion(outputs, test_labels)
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        # 计算预测
        _, predicted = torch.max(outputs.data, 1)
        
        print(f"✓ Training step successful:")
        print(f"  Loss: {loss.item():.4f}")
        print(f"  Predicted: {predicted}")
        print(f"  Actual: {test_labels}")
        
        # 检查梯度
        has_gradients = any(p.grad is not None and p.grad.abs().sum() > 0 for p in model.parameters())
        print(f"  Gradients computed: {'Yes' if has_gradients else 'No'}")
        
    except Exception as e:
        print(f"✗ Training step failed: {e}")
    
    print()

def test_model_compatibility():
    """测试模型与现有代码的兼容性"""
    print("=== Testing Model Compatibility ===")
    
    try:
        # 测试与VGG相同的接口
        from vgg3D_model import vgg
        
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        test_input = torch.randn(1, 1, 32, 64, 64).to(device)
        
        # 创建VGG和ResNet模型
        vgg_model = vgg(model_name="vgg16", in_channels=1, num_classes=2, init_weights=True).to(device)
        resnet_model = resnet3d(model_name="resnet18", in_channels=1, num_classes=2, init_weights=True).to(device)
        
        # 测试相同的输入
        vgg_model.eval()
        resnet_model.eval()
        
        with torch.no_grad():
            vgg_output = vgg_model(test_input)
            resnet_output = resnet_model(test_input)
        
        print(f"✓ Interface compatibility:")
        print(f"  VGG output shape: {vgg_output.shape}")
        print(f"  ResNet output shape: {resnet_output.shape}")
        print(f"  Both models produce same output shape: {vgg_output.shape == resnet_output.shape}")
        
    except Exception as e:
        print(f"✗ Compatibility test failed: {e}")
    
    print()

def test_memory_usage():
    """测试内存使用情况"""
    print("=== Testing Memory Usage ===")
    
    if torch.cuda.is_available():
        device = torch.device("cuda")
        
        models_to_test = ['resnet18', 'resnet34']
        input_sizes = [
            (1, 1, 32, 64, 64),    # 小输入
            (1, 1, 64, 128, 128),  # 标准输入
        ]
        
        for model_name in models_to_test:
            for input_size in input_sizes:
                try:
                    torch.cuda.empty_cache()  # 清空缓存
                    
                    model = resnet3d(model_name=model_name, in_channels=1, num_classes=2).to(device)
                    test_input = torch.randn(*input_size).to(device)
                    
                    # 记录内存使用
                    torch.cuda.synchronize()
                    memory_before = torch.cuda.memory_allocated() / 1024**2  # MB
                    
                    with torch.no_grad():
                        output = model(test_input)
                    
                    torch.cuda.synchronize()
                    memory_after = torch.cuda.memory_allocated() / 1024**2  # MB
                    
                    print(f"✓ {model_name.upper()} with input {input_size[2:]}:")
                    print(f"  Memory usage: {memory_after:.1f} MB")
                    
                    del model, test_input, output
                    torch.cuda.empty_cache()
                    
                except Exception as e:
                    print(f"✗ {model_name.upper()} with input {input_size[2:]}: {e}")
    else:
        print("CUDA not available, skipping memory test")
    
    print()

def main():
    """运行所有测试"""
    print("3D ResNet Model Testing")
    print("=" * 50)
    
    test_model_creation()
    test_forward_pass()
    test_training_step()
    test_model_compatibility()
    test_memory_usage()
    
    print("=" * 50)
    print("Testing completed!")
    print("\nIf all tests passed, the ResNet models are ready to use.")
    print("You can now run:")
    print("  python train_3D_resnet_cv.py")
    print("  python compare_models_cv.py")

if __name__ == "__main__":
    main()
