import torch.nn as nn
import torch
import torch.nn.functional as F


class GoogLeNet3D(nn.Module):  # 重命名为GoogLeNet3D
    def __init__(self, num_classes=2, aux_logits=True, init_weights=False, in_channels=1):  # 添加in_channels参数
        super(GoogLeNet3D, self).__init__()
        self.aux_logits = aux_logits
        self.in_channels = in_channels  # 保存输入通道数

        # 使用3D卷积
        self.conv1 = BasicConv3d(in_channels, 64, kernel_size=7, stride=2, padding=3)
        # 使用3D最大池化
        self.maxpool1 = nn.MaxPool3d(3, stride=2, ceil_mode=True)

        self.conv2 = BasicConv3d(64, 64, kernel_size=1)
        self.conv3 = BasicConv3d(64, 192, kernel_size=3, padding=1)
        self.maxpool2 = nn.MaxPool3d(3, stride=2, ceil_mode=True)

        # 使用3D Inception模块
        self.inception3a = Inception3D(192, 64, 96, 128, 16, 32, 32)
        self.inception3b = Inception3D(256, 128, 128, 192, 32, 96, 64)
        self.maxpool3 = nn.MaxPool3d(3, stride=2, ceil_mode=True)

        self.inception4a = Inception3D(480, 192, 96, 208, 16, 48, 64)
        self.inception4b = Inception3D(512, 160, 112, 224, 24, 64, 64)
        self.inception4c = Inception3D(512, 128, 128, 256, 24, 64, 64)
        self.inception4d = Inception3D(512, 112, 144, 288, 32, 64, 64)
        self.inception4e = Inception3D(528, 256, 160, 320, 32, 128, 128)
        self.maxpool4 = nn.MaxPool3d(3, stride=2, ceil_mode=True)

        self.inception5a = Inception3D(832, 256, 160, 320, 32, 128, 128)
        self.inception5b = Inception3D(832, 384, 192, 384, 48, 128, 128)

        if self.aux_logits:
            # 使用3D辅助分类器
            self.aux1 = InceptionAux3D(512, num_classes)
            self.aux2 = InceptionAux3D(528, num_classes)

        # 使用3D自适应平均池化
        self.avgpool = nn.AdaptiveAvgPool3d((1, 1, 1))  # 输出1x1x1的特征图
        self.dropout = nn.Dropout(0.4)
        self.fc = nn.Linear(1024, num_classes)  # 输入维度保持1024
        if init_weights:
            self._initialize_weights()

    def forward(self, x):
        # 输入形状: (batch_size, channels, depth, height, width)
        x = self.conv1(x)
        x = self.maxpool1(x)
        x = self.conv2(x)
        x = self.conv3(x)
        x = self.maxpool2(x)

        x = self.inception3a(x)
        x = self.inception3b(x)
        x = self.maxpool3(x)
        x = self.inception4a(x)
        
        if self.training and self.aux_logits:
            aux1 = self.aux1(x)

        x = self.inception4b(x)
        x = self.inception4c(x)
        x = self.inception4d(x)
        
        if self.training and self.aux_logits:
            aux2 = self.aux2(x)

        x = self.inception4e(x)
        x = self.maxpool4(x)
        x = self.inception5a(x)
        x = self.inception5b(x)

        x = self.avgpool(x)
        x = torch.flatten(x, 1)
        x = self.dropout(x)
        x = self.fc(x)
        
        if self.training and self.aux_logits:
            return x, aux2, aux1
        return x

    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv3d):  # 修改为Conv3d初始化
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                nn.init.constant_(m.bias, 0)


class Inception3D(nn.Module):  # 3D Inception模块
    def __init__(self, in_channels, ch1x1, ch3x3red, ch3x3, ch5x5red, ch5x5, pool_proj):
        super(Inception3D, self).__init__()

        # 分支1: 1x1x1卷积
        self.branch1 = BasicConv3d(in_channels, ch1x1, kernel_size=1)

        # 分支2: 1x1x1卷积 + 3x3x3卷积
        self.branch2 = nn.Sequential(
            BasicConv3d(in_channels, ch3x3red, kernel_size=1),
            BasicConv3d(ch3x3red, ch3x3, kernel_size=3, padding=1)  # 保持尺寸
        )

        # 分支3: 1x1x1卷积 + 5x5x5卷积
        self.branch3 = nn.Sequential(
            BasicConv3d(in_channels, ch5x5red, kernel_size=1),
            BasicConv3d(ch5x5red, ch5x5, kernel_size=5, padding=2)  # 保持尺寸
        )

        # 分支4: 3x3x3最大池化 + 1x1x1卷积
        self.branch4 = nn.Sequential(
            nn.MaxPool3d(kernel_size=3, stride=1, padding=1),
            BasicConv3d(in_channels, pool_proj, kernel_size=1)
        )

    def forward(self, x):
        branch1 = self.branch1(x)
        branch2 = self.branch2(x)
        branch3 = self.branch3(x)
        branch4 = self.branch4(x)

        outputs = [branch1, branch2, branch3, branch4]
        return torch.cat(outputs, 1)  # 在通道维度上拼接


class InceptionAux3D(nn.Module):  # 3D辅助分类器
    def __init__(self, in_channels, num_classes):
        super(InceptionAux3D, self).__init__()
        # 使用3D平均池化
        self.averagePool = nn.AvgPool3d(kernel_size=4, stride=3)
        self.conv = BasicConv3d(in_channels, 128, kernel_size=1)  # 输出[batch, 128, 4, 4, 4]
        
        # 调整全连接层输入维度 (128 * 4 * 4 * 4 = 2048)
        self.fc1 = nn.Linear(2048, 1024)
        self.fc2 = nn.Linear(1024, num_classes)

    def forward(self, x):
        # 输入形状: (batch, channels, depth, height, width)
        x = self.averagePool(x)
        x = self.conv(x)
        
        # 展平特征图
        x = torch.flatten(x, 1)
        x = F.dropout(x, 0.5, training=self.training)
        x = F.relu(self.fc1(x), inplace=True)
        x = F.dropout(x, 0.5, training=self.training)
        x = self.fc2(x)
        return x


class BasicConv3d(nn.Module):  # 3D基础卷积块
    def __init__(self, in_channels, out_channels, **kwargs):
        super(BasicConv3d, self).__init__()
        # 使用3D卷积
        self.conv = nn.Conv3d(in_channels, out_channels, **kwargs)
        self.relu = nn.ReLU(inplace=True)

    def forward(self, x):
        x = self.conv(x)
        x = self.relu(x)
        return x