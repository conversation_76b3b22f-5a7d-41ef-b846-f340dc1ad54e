import torch
import torch.nn as nn
import torch.nn.functional as F

class BasicBlock3D(nn.Module):
    """3D ResNet基础块 (用于ResNet18和ResNet34)"""
    expansion = 1

    def __init__(self, in_channels, out_channels, stride=1, downsample=None):
        super(BasicBlock3D, self).__init__()
        
        # 第一个3x3x3卷积
        self.conv1 = nn.Conv3d(in_channels, out_channels, kernel_size=3, 
                              stride=stride, padding=1, bias=False)
        self.bn1 = nn.BatchNorm3d(out_channels)
        
        # 第二个3x3x3卷积
        self.conv2 = nn.Conv3d(out_channels, out_channels, kernel_size=3, 
                              stride=1, padding=1, bias=False)
        self.bn2 = nn.BatchNorm3d(out_channels)
        
        self.relu = nn.ReLU(inplace=True)
        self.downsample = downsample

    def forward(self, x):
        identity = x

        # 第一个卷积块
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)

        # 第二个卷积块
        out = self.conv2(out)
        out = self.bn2(out)

        # 残差连接
        if self.downsample is not None:
            identity = self.downsample(x)

        out += identity
        out = self.relu(out)

        return out


class Bottleneck3D(nn.Module):
    """3D ResNet瓶颈块 (用于ResNet50, ResNet101, ResNet152)"""
    expansion = 4

    def __init__(self, in_channels, out_channels, stride=1, downsample=None):
        super(Bottleneck3D, self).__init__()
        
        # 1x1x1卷积
        self.conv1 = nn.Conv3d(in_channels, out_channels, kernel_size=1, bias=False)
        self.bn1 = nn.BatchNorm3d(out_channels)
        
        # 3x3x3卷积
        self.conv2 = nn.Conv3d(out_channels, out_channels, kernel_size=3, 
                              stride=stride, padding=1, bias=False)
        self.bn2 = nn.BatchNorm3d(out_channels)
        
        # 1x1x1卷积
        self.conv3 = nn.Conv3d(out_channels, out_channels * self.expansion, 
                              kernel_size=1, bias=False)
        self.bn3 = nn.BatchNorm3d(out_channels * self.expansion)
        
        self.relu = nn.ReLU(inplace=True)
        self.downsample = downsample

    def forward(self, x):
        identity = x

        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)

        out = self.conv2(out)
        out = self.bn2(out)
        out = self.relu(out)

        out = self.conv3(out)
        out = self.bn3(out)

        if self.downsample is not None:
            identity = self.downsample(x)

        out += identity
        out = self.relu(out)

        return out


class ResNet3D(nn.Module):
    """3D ResNet模型"""
    
    def __init__(self, block, layers, num_classes=2, in_channels=1, init_weights=True):
        super(ResNet3D, self).__init__()
        
        self.in_channels = 64
        
        # 优化的初始卷积层 - 使用更小的卷积核减少参数
        self.conv1 = nn.Conv3d(in_channels, 64, kernel_size=(3, 7, 7), stride=(1, 2, 2),
                              padding=(1, 3, 3), bias=False)  # 深度方向使用3x3，空间方向使用7x7
        self.bn1 = nn.BatchNorm3d(64)
        self.relu = nn.ReLU(inplace=True)
        self.maxpool = nn.MaxPool3d(kernel_size=(1, 3, 3), stride=(1, 2, 2), padding=(0, 1, 1))
        
        # ResNet层
        self.layer1 = self._make_layer(block, 64, layers[0])
        self.layer2 = self._make_layer(block, 128, layers[1], stride=2)
        self.layer3 = self._make_layer(block, 256, layers[2], stride=2)
        self.layer4 = self._make_layer(block, 512, layers[3], stride=2)
        
        # 自适应平均池化和分类器
        self.adaptive_avg_pool = nn.AdaptiveAvgPool3d((1, 1, 1))
        self.fc = nn.Linear(512 * block.expansion, num_classes)
        
        if init_weights:
            self._initialize_weights()

    def _make_layer(self, block, out_channels, blocks, stride=1):
        downsample = None
        
        # 如果步长不为1或输入输出通道数不匹配，需要下采样
        if stride != 1 or self.in_channels != out_channels * block.expansion:
            downsample = nn.Sequential(
                nn.Conv3d(self.in_channels, out_channels * block.expansion,
                         kernel_size=1, stride=stride, bias=False),
                nn.BatchNorm3d(out_channels * block.expansion),
            )

        layers = []
        layers.append(block(self.in_channels, out_channels, stride, downsample))
        self.in_channels = out_channels * block.expansion
        
        for _ in range(1, blocks):
            layers.append(block(self.in_channels, out_channels))

        return nn.Sequential(*layers)

    def forward(self, x):
        # 输入形状: (batch_size, channels, depth, height, width)
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.maxpool(x)

        x = self.layer1(x)
        x = self.layer2(x)
        x = self.layer3(x)
        x = self.layer4(x)

        x = self.adaptive_avg_pool(x)
        x = torch.flatten(x, 1)
        x = self.fc(x)

        return x

    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv3d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm3d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                nn.init.constant_(m.bias, 0)


def resnet18_3d(in_channels=1, num_classes=2, init_weights=True):
    """构建3D ResNet18模型"""
    return ResNet3D(BasicBlock3D, [2, 2, 2, 2], num_classes=num_classes, 
                    in_channels=in_channels, init_weights=init_weights)


def resnet34_3d(in_channels=1, num_classes=2, init_weights=True):
    """构建3D ResNet34模型"""
    return ResNet3D(BasicBlock3D, [3, 4, 6, 3], num_classes=num_classes, 
                    in_channels=in_channels, init_weights=init_weights)


def resnet50_3d(in_channels=1, num_classes=2, init_weights=True):
    """构建3D ResNet50模型"""
    return ResNet3D(Bottleneck3D, [3, 4, 6, 3], num_classes=num_classes, 
                    in_channels=in_channels, init_weights=init_weights)


def resnet101_3d(in_channels=1, num_classes=2, init_weights=True):
    """构建3D ResNet101模型"""
    return ResNet3D(Bottleneck3D, [3, 4, 23, 3], num_classes=num_classes, 
                    in_channels=in_channels, init_weights=init_weights)


def resnet152_3d(in_channels=1, num_classes=2, init_weights=True):
    """构建3D ResNet152模型"""
    return ResNet3D(Bottleneck3D, [3, 8, 36, 3], num_classes=num_classes, 
                    in_channels=in_channels, init_weights=init_weights)


# 为了与现有代码兼容，提供统一的接口
def resnet3d(model_name="resnet34", in_channels=1, num_classes=2, init_weights=True):
    """
    创建3D ResNet模型的统一接口
    
    Args:
        model_name (str): 模型名称，支持 'resnet18', 'resnet34', 'resnet50', 'resnet101', 'resnet152'
        in_channels (int): 输入通道数，默认为1（MRI单通道）
        num_classes (int): 分类类别数，默认为2（HC vs MCI）
        init_weights (bool): 是否初始化权重
    
    Returns:
        ResNet3D: 3D ResNet模型实例
    """
    model_dict = {
        'resnet18': resnet18_3d,
        'resnet34': resnet34_3d,
        'resnet50': resnet50_3d,
        'resnet101': resnet101_3d,
        'resnet152': resnet152_3d
    }
    
    if model_name not in model_dict:
        raise ValueError(f"Unsupported model: {model_name}. "
                        f"Supported models: {list(model_dict.keys())}")
    
    return model_dict[model_name](in_channels=in_channels, 
                                 num_classes=num_classes, 
                                 init_weights=init_weights)


if __name__ == "__main__":
    # 测试模型
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 创建测试输入 (batch_size=1, channels=1, depth=64, height=128, width=128)
    test_input = torch.randn(1, 1, 64, 128, 128).to(device)
    
    # 测试不同的ResNet模型
    models_to_test = ['resnet18', 'resnet34', 'resnet50']
    
    for model_name in models_to_test:
        print(f"\n=== Testing {model_name.upper()} ===")
        model = resnet3d(model_name=model_name, in_channels=1, num_classes=2).to(device)
        
        # 计算参数数量
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"Total parameters: {total_params:,}")
        print(f"Trainable parameters: {trainable_params:,}")
        
        # 前向传播测试
        model.eval()
        with torch.no_grad():
            output = model(test_input)
            print(f"Input shape: {test_input.shape}")
            print(f"Output shape: {output.shape}")
            print(f"Output: {output}")
        
        del model  # 释放内存
