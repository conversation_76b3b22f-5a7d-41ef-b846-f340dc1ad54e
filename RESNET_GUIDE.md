# 3D ResNet模型使用指南

## 概述

本项目现在支持3D ResNet模型，专门为3D MRI数据分类任务设计。ResNet（残差网络）通过引入残差连接解决了深度网络的梯度消失问题，通常比VGG网络具有更好的性能。

## 文件说明

### 新增文件

1. **resnet3D_model.py** - 3D ResNet模型定义
   - 支持ResNet18, ResNet34, ResNet50, ResNet101, ResNet152
   - 专门为3D医学图像数据优化
   - 与现有VGG模型接口兼容

2. **train_3D_resnet_cv.py** - ResNet模型训练脚本
   - 支持交互式模型选择
   - 包含详细的模型信息显示
   - 自动保存训练结果

3. **compare_models_cv.py** - 模型性能比较脚本
   - 同时比较VGG和ResNet模型
   - 自动生成性能对比报告
   - 包含参数数量和训练时间统计

### 更新文件

4. **train_3D_cv_grid_search.py** - 已更新支持ResNet模型的网格搜索

## 支持的ResNet模型

| 模型 | 层数 | 参数量 | 推荐用途 |
|------|------|--------|----------|
| ResNet18 | 18 | ~11M | 快速实验、资源受限 |
| ResNet34 | 34 | ~21M | **推荐选择**、平衡性能和效率 |
| ResNet50 | 50 | ~23M | 高精度要求 |
| ResNet101 | 101 | ~42M | 最高精度、充足资源 |
| ResNet152 | 152 | ~58M | 研究用途 |

## 使用方法

### 1. 基础ResNet训练

```bash
python train_3D_resnet_cv.py
```

程序会提示选择模型：
```
Available ResNet models:
1. ResNet18 (11M parameters)
2. ResNet34 (21M parameters) - Recommended
3. ResNet50 (23M parameters)
4. ResNet101 (42M parameters)

Choose model (1-4, default=2):
```

### 2. 模型性能比较

```bash
python compare_models_cv.py
```

这将自动比较以下模型：
- VGG16
- ResNet18
- ResNet34
- ResNet50

### 3. 网格搜索（包含ResNet）

```bash
python train_3D_cv_grid_search.py
```

选择选项1进行网格搜索，现在包含ResNet模型。

## ResNet vs VGG 对比

### ResNet优势
- **残差连接**: 解决梯度消失问题，支持更深的网络
- **更好的特征学习**: 残差块能学习更复杂的特征表示
- **参数效率**: 相比VGG，ResNet在相似参数量下通常性能更好
- **训练稳定性**: 残差连接使训练更稳定

### VGG优势
- **简单结构**: 更容易理解和调试
- **成熟稳定**: 在医学图像领域应用广泛
- **内存友好**: 某些情况下内存使用更少

## 模型架构详解

### ResNet34架构
```
输入: (1, 64, 128, 128) - 单通道3D MRI
├── Conv3d(7x7x7, stride=2) + BN + ReLU
├── MaxPool3d(3x3x3, stride=2)
├── Layer1: 3 × BasicBlock (64 channels)
├── Layer2: 4 × BasicBlock (128 channels, stride=2)
├── Layer3: 6 × BasicBlock (256 channels, stride=2)
├── Layer4: 3 × BasicBlock (512 channels, stride=2)
├── AdaptiveAvgPool3d(1x1x1)
└── Linear(512 → 2) - 分类层
```

### BasicBlock结构
```
输入 → Conv3d(3x3x3) → BN → ReLU → Conv3d(3x3x3) → BN → (+残差) → ReLU → 输出
  ↓                                                        ↑
  └─────────────── 残差连接 ──────────────────────────────┘
```

## 性能优化建议

### 1. 内存优化
```python
# 如果GPU内存不足，可以：
batch_size = 1          # 减小批次大小
model_name = "resnet18" # 使用较小的模型
```

### 2. 训练加速
```python
# 使用混合精度训练（需要较新的GPU）
from torch.cuda.amp import autocast, GradScaler

scaler = GradScaler()
with autocast():
    outputs = model(inputs)
    loss = criterion(outputs, targets)
```

### 3. 学习率调整
```python
# ResNet通常对学习率更敏感
optimizer = optim.Adam(model.parameters(), lr=0.0001)  # 较小的学习率
scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=10, gamma=0.1)
```

## 实验建议

### 1. 模型选择流程
1. **快速测试**: 使用ResNet18进行初步实验
2. **性能对比**: 运行`compare_models_cv.py`比较所有模型
3. **精细调优**: 选择最佳模型进行网格搜索
4. **最终训练**: 使用最佳超参数进行完整训练

### 2. 超参数建议
```python
# ResNet推荐超参数
learning_rate = 0.0001      # 较小的学习率
weight_decay = 0.0001       # 适度的权重衰减
batch_size = 1              # 根据GPU内存调整
epochs = 30                 # 充分训练
optimizer = 'Adam'          # Adam优化器
scheduler = 'StepLR'        # 阶梯式学习率衰减
```

## 结果分析

### 1. 查看训练结果
```bash
# 查看ResNet34结果
cat resnet_resnet34_cv_results.json

# 查看模型比较结果
cat model_comparison_results.json
```

### 2. 模型文件
训练完成后会生成：
- `3d_resnet34_fold_1.pth` - 第1折最佳模型
- `3d_resnet34_fold_2.pth` - 第2折最佳模型
- `3d_resnet34_fold_3.pth` - 第3折最佳模型

## 常见问题

### Q: ResNet比VGG慢吗？
A: 通常ResNet训练速度与VGG相近，但由于残差连接，可能需要稍多的计算。

### Q: 应该选择哪个ResNet模型？
A: 推荐从ResNet34开始，它在性能和效率之间有很好的平衡。

### Q: 如何处理内存不足？
A: 
1. 减小batch_size到1
2. 使用ResNet18而不是更大的模型
3. 减小输入图像尺寸

### Q: ResNet的准确率一定比VGG高吗？
A: 不一定，这取决于具体的数据集和超参数。建议使用`compare_models_cv.py`进行实际比较。

## 下一步

1. **运行模型比较**: `python compare_models_cv.py`
2. **选择最佳模型**: 基于比较结果选择性能最好的模型
3. **超参数优化**: 对选定模型进行网格搜索
4. **最终评估**: 使用最佳配置进行完整的训练和评估

## 技术细节

### 残差连接的数学表示
```
y = F(x) + x
```
其中F(x)是残差函数，x是输入。这种设计使网络能够学习恒等映射，解决了深度网络的退化问题。

### 3D适配
所有2D操作都已适配为3D：
- `Conv2d` → `Conv3d`
- `BatchNorm2d` → `BatchNorm3d`
- `MaxPool2d` → `MaxPool3d`
- `AdaptiveAvgPool2d` → `AdaptiveAvgPool3d`

这确保了模型能够充分利用3D MRI数据的空间信息。
