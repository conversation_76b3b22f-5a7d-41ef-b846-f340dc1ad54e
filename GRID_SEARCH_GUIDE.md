# 网格搜索超参数优化指南

## 概述

本项目现在包含三个版本的训练脚本，支持不同级别的超参数优化：

1. **train_3D_cv.py** - 原始版本，使用固定的默认超参数
2. **train_3D_cv_grid_search.py** - 完整网格搜索版本，支持所有超参数优化
3. **train_3D_cv_simple_grid.py** - 简化网格搜索版本，只优化关键超参数

## 文件说明

### 1. train_3D_cv.py
- **用途**: 快速训练和测试
- **特点**: 使用预设的默认超参数
- **运行时间**: 最短
- **推荐场景**: 初次测试、快速验证

### 2. train_3D_cv_grid_search.py
- **用途**: 全面的超参数优化
- **特点**: 搜索所有可能的超参数组合
- **运行时间**: 最长（可能需要数小时到数天）
- **推荐场景**: 最终模型优化、发表论文

### 3. train_3D_cv_simple_grid.py
- **用途**: 快速找到较好的超参数
- **特点**: 只搜索最重要的超参数（学习率、权重衰减、优化器）
- **运行时间**: 中等（通常几小时）
- **推荐场景**: 日常实验、快速改进

## 超参数说明

### 搜索的超参数

#### 完整网格搜索 (train_3D_cv_grid_search.py)
```python
param_grid = {
    'learning_rate': [0.0001, 0.0005, 0.001],      # 学习率
    'batch_size': [1, 2],                          # 批次大小
    'optimizer': ['Adam', 'AdamW'],                # 优化器
    'weight_decay': [0.0, 0.0001, 0.001],         # 权重衰减
    'epochs': [20, 30],                            # 训练轮数
    'model_name': ['vgg16'],                       # 模型架构
    'scheduler': [None, 'StepLR', 'CosineAnnealingLR']  # 学习率调度器
}
```

#### 简化网格搜索 (train_3D_cv_simple_grid.py)
```python
learning_rates = [0.0001, 0.0005, 0.001]         # 学习率
weight_decays = [0.0, 0.0001, 0.001]             # 权重衰减
optimizers = ['Adam', 'AdamW']                    # 优化器
batch_size = 1                                   # 固定批次大小
epochs = 20                                      # 固定训练轮数
```

## 使用方法

### 1. 简化网格搜索（推荐开始）

```bash
python train_3D_cv_simple_grid.py
```

这将自动运行网格搜索，无需用户交互。结果保存在 `simple_grid_search_results.json`。

### 2. 完整网格搜索

```bash
python train_3D_cv_grid_search.py
```

程序会询问您选择训练模式：
- 输入 `1`: 执行网格搜索
- 输入 `2`: 使用默认参数训练

### 3. 标准训练

```bash
python train_3D_cv.py
```

使用固定的默认超参数进行训练。

## 结果文件

### 网格搜索结果文件

1. **simple_grid_search_results.json** - 简化网格搜索结果
2. **grid_search_results.json** - 完整网格搜索结果
3. **final_cv_results.json** - 最终交叉验证结果

### 结果文件结构

```json
{
  "best_params": {
    "learning_rate": 0.0005,
    "weight_decay": 0.0001,
    "optimizer": "AdamW",
    "batch_size": 1,
    "epochs": 20
  },
  "best_score": 0.8542,
  "all_results": [
    {
      "learning_rate": 0.0001,
      "weight_decay": 0.0,
      "optimizer": "Adam",
      "mean_score": 0.8234,
      "std_score": 0.0156,
      "fold_scores": [0.8100, 0.8200, 0.8400],
      "time_elapsed": 1234.56
    }
  ]
}
```

## 性能优化建议

### 1. 硬件要求
- **GPU**: 推荐使用CUDA兼容的GPU
- **内存**: 至少16GB RAM
- **存储**: SSD存储以加快数据加载

### 2. 参数调整建议

#### 如果内存不足：
- 减少 `batch_size` 到 1
- 减少 `epochs` 数量
- 使用更小的图像尺寸

#### 如果时间有限：
- 使用简化网格搜索
- 减少超参数选择范围
- 减少交叉验证折数

#### 如果精度不够：
- 增加 `epochs` 数量
- 尝试更多学习率值
- 添加更多优化器选择

### 3. 监控训练过程

```bash
# 监控GPU使用情况
watch -n 1 nvidia-smi

# 监控训练日志
tail -f nohup.out
```

## 常见问题

### Q: 网格搜索需要多长时间？
A: 
- 简化网格搜索：2-6小时（18个组合）
- 完整网格搜索：1-3天（252个组合）

### Q: 如何选择最佳超参数？
A: 查看生成的JSON文件中的 `best_params` 字段，这是基于交叉验证平均分数选择的最佳组合。

### Q: 可以中断网格搜索吗？
A: 可以使用 Ctrl+C 中断，已完成的结果会保存在JSON文件中。

### Q: 如何自定义超参数范围？
A: 编辑对应脚本中的 `param_grid` 或参数列表，添加或删除想要测试的值。

## 下一步建议

1. **首次使用**: 运行简化网格搜索找到较好的超参数
2. **精细调优**: 基于简化搜索结果，在完整网格搜索中缩小参数范围
3. **最终训练**: 使用找到的最佳超参数进行完整的训练和评估

## 注意事项

- 网格搜索会消耗大量计算资源，建议在空闲时间运行
- 确保有足够的磁盘空间保存模型和结果文件
- 定期备份重要的结果文件
- 考虑使用 `nohup` 命令在后台运行长时间的搜索任务
