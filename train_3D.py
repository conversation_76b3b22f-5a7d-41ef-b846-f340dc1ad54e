import os
import sys
import json
import numpy as np

import torch
import torch.nn as nn
import torch.optim as optim
from tqdm import tqdm

from vgg3D_model import vgg

# 导入医学图像处理库
import nibabel as nib
from torch.utils.data import Dataset, DataLoader
from monai.transforms import (
    Compose, LoadImage, EnsureChannelFirst, RandRotate, RandFlip, 
    RandZoom, RandAffine, ScaleIntensity, Resize, ToTensor
)

class NiftiDataset(Dataset):
    """自定义数据集类，用于加载NIfTI格式的3D MRI数据"""
    def __init__(self, data_dir, classes, transform=None):
        """
        Args:
            data_dir (string): 包含类别子目录的根目录
            classes (list): 类别列表
            transform (callable, optional): 应用于样本的可选变换
        """
        self.data = []
        self.transform = transform
        
        # 遍历每个类别的目录
        for class_idx, class_name in enumerate(classes):
            class_dir = os.path.join(data_dir, class_name)
            if not os.path.isdir(class_dir):
                continue
                
            # 收集所有NIfTI文件
            for file_name in os.listdir(class_dir):
                if file_name.endswith('.nii') or file_name.endswith('.nii.gz'):
                    self.data.append({
                        'image_path': os.path.join(class_dir, file_name),
                        'label': class_idx
                    })
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        sample = self.data[idx]
        
        # 加载NIfTI文件
        img = nib.load(sample['image_path'])
        image_data = img.get_fdata(dtype=np.float32)
        
        # 应用变换
        if self.transform:
            image_data = self.transform(image_data)
            
        return image_data, sample['label']

def add_channel_dim(x):
    return x[None, ...]

def main():
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print(f"Using {device} device.")
    
    # 定义3D数据预处理和增强
    train_transform = Compose([
        add_channel_dim,  # 添加通道维度 (C, D, H, W)
        ScaleIntensity(minv=0.0, maxv=1.0),  # 归一化到[0,1]
        RandRotate(range_x=15, range_y=15, range_z=15, prob=0.5),  # 随机旋转
        RandFlip(prob=0.5, spatial_axis=0),  # 随机翻转
        RandZoom(min_zoom=0.9, max_zoom=1.1, prob=0.5),  # 随机缩放
        Resize(spatial_size=(64, 128, 128)),  # 调整尺寸 (深度,高度,宽度)
        ToTensor()  # 转换为张量
    ])
    
    val_transform = Compose([
        add_channel_dim,  # 添加通道维度
        ScaleIntensity(minv=0.0, maxv=1.0),  # 归一化到[0,1]
        Resize(spatial_size=(64, 128, 128)),  # 调整尺寸
        ToTensor()  # 转换为张量
    ])
    
    # 数据目录设置
    data_root = os.path.abspath(os.path.join(os.getcwd(), "data"))  # 数据根目录
    train_dir = os.path.join(data_root, "train")  # 训练数据目录
    val_dir = os.path.join(data_root, "val")  # 验证数据目录
    
    # 类别定义 (根据实际数据修改)
    classes = ['HC', 'MCI']  # 示例: NC = 正常对照组, AD = 阿尔茨海默病
    
    # 创建数据集
    train_dataset = NiftiDataset(train_dir, classes, transform=train_transform)
    val_dataset = NiftiDataset(val_dir, classes, transform=val_transform)
    
    train_num = len(train_dataset)
    val_num = len(val_dataset)
    
    # 创建数据加载器
    batch_size = 1 # 由于3D数据较大，减小batch_size
    nw = min(os.cpu_count(), batch_size)  # 工作线程数
    print(f'Using {nw} dataloader workers')
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, 
                             shuffle=True, num_workers=nw)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, 
                           shuffle=False, num_workers=nw)
    
    print(f"Using {train_num} 3D MRI volumes for training, {val_num} for validation.")
    
    # 创建模型 (使用3D版本)
    net = vgg(model_name="vgg16", in_channels=1, num_classes=len(classes), init_weights=True)
    net.to(device)
    
    # 定义损失函数和优化器
    loss_function = nn.CrossEntropyLoss()
    optimizer = optim.Adam(net.parameters(), lr=0.0001)  # 减小学习率
    
    # 训练参数
    epochs = 30
    best_acc = 0.0
    save_path = './3d_vgg.pth'
    train_steps = len(train_loader)
    
    # 训练循环
    for epoch in range(epochs):
        # 训练阶段
        net.train()
        running_loss = 0.0
        train_bar = tqdm(train_loader, file=sys.stdout)
        
        for step, data in enumerate(train_bar):
            images, labels = data
            images, labels = images.to(device), labels.to(device)
            
            optimizer.zero_grad()
            
            # 训练时使用辅助分类器
            
            logits = net(images)
            loss = loss_function(logits, labels)
            
            loss.backward()
            optimizer.step()
            
            # 统计损失
            running_loss += loss.item()
            
            train_bar.desc = f"train epoch[{epoch+1}/{epochs}] loss:{loss.item():.3f}"
        
        # 验证阶段
        net.eval()
        acc = 0.0
        
        with torch.no_grad():
            val_bar = tqdm(val_loader, file=sys.stdout)
            for val_data in val_bar:
                val_images, val_labels = val_data
                val_images, val_labels = val_images.to(device), val_labels.to(device)
                
                outputs = net(val_images)
                if isinstance(outputs, tuple):  # 如果有辅助输出，只取主输出
                    outputs = outputs[0]
                
                predict_y = torch.max(outputs, dim=1)[1]
                acc += torch.eq(predict_y, val_labels).sum().item()
        
        val_accurate = acc / val_num
        avg_loss = running_loss / train_steps
        print(f'[epoch {epoch+1}] train_loss: {avg_loss:.3f}  val_accuracy: {val_accurate:.3f}')
        
        # 保存最佳模型
        if val_accurate > best_acc:
            best_acc = val_accurate
            torch.save(net.state_dict(), save_path)
            print(f"Saved new best model with accuracy: {best_acc:.3f}")
    
    print('Finished Training')


if __name__ == '__main__':
    main()