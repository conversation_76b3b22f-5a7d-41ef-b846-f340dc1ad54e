# Linux Installation Guide for 3D MRI Classification Project

## System Requirements

- Python 3.8 or higher
- CUDA-capable GPU (recommended for training)
- At least 8GB RAM (16GB+ recommended)
- Ubuntu 18.04+ / CentOS 7+ / other modern Linux distributions

## Installation Steps

### 1. Create Virtual Environment (Recommended)

```bash
# Using conda (recommended)
conda create -n mri_classification python=3.8
conda activate mri_classification

# Or using venv
python3 -m venv mri_classification
source mri_classification/bin/activate
```

### 2. Install PyTorch with CUDA Support

For CUDA 11.8 (check your CUDA version with `nvidia-smi`):
```bash
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
```

For CPU-only installation:
```bash
pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu
```

### 3. Install Other Dependencies

```bash
pip install -r requirements.txt
```

### 4. Verify Installation

```bash
python -c "import torch; print(f'PyTorch version: {torch.__version__}')"
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
python -c "import monai; print(f'MONAI version: {monai.__version__}')"
python -c "import nibabel; print('nibabel imported successfully')"
```

## Data Structure

Ensure your data is organized as follows:
```
project_root/
├── HC/                 # Healthy Control NIfTI files
│   ├── *.nii
│   └── *.nii.gz
├── MCI/                # Mild Cognitive Impairment NIfTI files
│   ├── *.nii
│   └── *.nii.gz
├── train_3D_cv.py      # Main training script
├── vgg3D_model.py      # 3D VGG model definition
└── requirements.txt    # Dependencies
```

## Running the Training

```bash
# Make sure you're in the project directory and virtual environment is activated
python train_3D_cv.py
```

## Troubleshooting

### CUDA Issues
- Check CUDA version: `nvidia-smi`
- Install appropriate PyTorch version for your CUDA
- Ensure NVIDIA drivers are properly installed

### Memory Issues
- Reduce batch size in the training script
- Use CPU training if GPU memory is insufficient
- Close other applications to free up RAM

### Permission Issues
- Ensure read permissions for data directories
- Use `sudo` if needed for system-wide installations (not recommended for pip)

## Performance Tips

1. **Use SSD storage** for faster data loading
2. **Increase num_workers** in DataLoader if you have multiple CPU cores
3. **Use mixed precision training** to reduce memory usage
4. **Monitor GPU utilization** with `nvidia-smi` or `watch -n 1 nvidia-smi`
